"""
Agent Output Schema for CBRE Validation Agent
Defines data structures for validation results and outputs
"""

from dataclasses import dataclass
from typing import Dict, List, Any, Optional
from datetime import datetime
import json


@dataclass
class ValidationResult:
    """Single row validation result"""
    row_index: int
    is_correct: bool
    reason: str
    row_data: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'row_index': self.row_index,
            'is_correct': self.is_correct,
            'reason': self.reason,
            'row_data': self.row_data
        }


@dataclass
class ValidationSummary:
    """Overall validation summary"""
    total_rows: int
    valid_rows: int
    invalid_rows: int
    validation_rate: float
    timestamp: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'total_rows': self.total_rows,
            'valid_rows': self.valid_rows,
            'invalid_rows': self.invalid_rows,
            'validation_rate': self.validation_rate,
            'timestamp': self.timestamp
        }


@dataclass
class DataValidationOutput:
    """Complete data validation output"""
    summary: ValidationSummary
    results: List[ValidationResult]
    validation_rules: List[Dict[str, Any]]
    input_file: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON output"""
        return {
            'summary': self.summary.to_dict(),
            'results': [result.to_dict() for result in self.results],
            'validation_rules': self.validation_rules,
            'input_file': self.input_file
        }

    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), indent=2, ensure_ascii=False)

    @classmethod
    def create_summary(cls, results: List[ValidationResult]) -> ValidationSummary:
        """Create validation summary from results"""
        total_rows = len(results)
        valid_rows = sum(1 for result in results if result.is_correct)
        invalid_rows = total_rows - valid_rows
        validation_rate = (valid_rows / total_rows * 100) if total_rows > 0 else 0

        return ValidationSummary(
            total_rows=total_rows,
            valid_rows=valid_rows,
            invalid_rows=invalid_rows,
            validation_rate=round(validation_rate, 2),
            timestamp=datetime.now().isoformat()
        )