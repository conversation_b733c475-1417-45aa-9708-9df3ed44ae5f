"""
File Utilities for CBRE Validation Agent
Handles file operations for Excel, JSON, and text files
"""

import os
import json
import pandas as pd
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path


class FileUtils:
    """Utility class for file operations"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def read_excel_file(self, file_path: str) -> pd.DataFrame:
        """
        Read Excel file (xlsx or xls format)

        Args:
            file_path: Path to Excel file

        Returns:
            DataFrame containing Excel data
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Excel file not found: {file_path}")

            # Determine file extension and read accordingly
            file_ext = Path(file_path).suffix.lower()

            if file_ext == '.xlsx':
                df = pd.read_excel(file_path, engine='openpyxl')
            elif file_ext == '.xls':
                df = pd.read_excel(file_path, engine='xlrd')
            else:
                raise ValueError(f"Unsupported file format: {file_ext}. Only .xlsx and .xls are supported.")

            self.logger.info(f"Successfully read Excel file: {file_path} ({len(df)} rows)")
            return df

        except Exception as e:
            self.logger.error(f"Error reading Excel file {file_path}: {e}")
            raise

    def read_json_file(self, file_path: str) -> Any:
        """
        Read JSON file

        Args:
            file_path: Path to JSON file

        Returns:
            Parsed JSON data
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"JSON file not found: {file_path}")

            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)

            self.logger.info(f"Successfully read JSON file: {file_path}")
            return data

        except Exception as e:
            self.logger.error(f"Error reading JSON file {file_path}: {e}")
            raise

    def read_text_file(self, file_path: str) -> str:
        """
        Read text file

        Args:
            file_path: Path to text file

        Returns:
            File content as string
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Text file not found: {file_path}")

            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()

            self.logger.info(f"Successfully read text file: {file_path}")
            return content

        except Exception as e:
            self.logger.error(f"Error reading text file {file_path}: {e}")
            raise

    def save_json_file(self, data: Any, file_path: str) -> None:
        """
        Save data to JSON file

        Args:
            data: Data to save
            file_path: Path to save JSON file
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, 'w', encoding='utf-8') as file:
                json.dump(data, file, indent=2, ensure_ascii=False)

            self.logger.info(f"Successfully saved JSON file: {file_path}")

        except Exception as e:
            self.logger.error(f"Error saving JSON file {file_path}: {e}")
            raise

    def save_excel_file(self, df: pd.DataFrame, file_path: str) -> None:
        """
        Save DataFrame to Excel file

        Args:
            df: DataFrame to save
            file_path: Path to save Excel file
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            df.to_excel(file_path, index=False, engine='openpyxl')

            self.logger.info(f"Successfully saved Excel file: {file_path} ({len(df)} rows)")

        except Exception as e:
            self.logger.error(f"Error saving Excel file {file_path}: {e}")
            raise

    def validate_file_exists(self, file_path: str) -> bool:
        """
        Check if file exists

        Args:
            file_path: Path to check

        Returns:
            True if file exists, False otherwise
        """
        return os.path.exists(file_path)

    def get_file_extension(self, file_path: str) -> str:
        """
        Get file extension

        Args:
            file_path: Path to file

        Returns:
            File extension (lowercase)
        """
        return Path(file_path).suffix.lower()