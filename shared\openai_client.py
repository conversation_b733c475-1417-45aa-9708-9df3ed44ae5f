"""
OpenAI Client for CBRE Validation Agent
Handles OpenAI API interactions with configuration management
"""

import os
import yaml
import logging
from openai import OpenAI
from typing import Dict, Any, Optional


class OpenAIClient:
    """OpenAI client with configuration management"""

    def __init__(self, config_path: str = "config/model_config.yaml"):
        """
        Initialize OpenAI client with configuration

        Args:
            config_path: Path to model configuration YAML file
        """
        self.config = self._load_config(config_path)
        self.client = self._initialize_client()
        self.logger = logging.getLogger(__name__)

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as file:
                config = yaml.safe_load(file)
                return config.get('openai', {})
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML configuration: {e}")

    def _initialize_client(self) -> OpenAI:
        """Initialize OpenAI client with API key"""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable not set")

        return OpenAI(api_key=api_key)

    def get_completion(self, prompt: str, max_tokens: Optional[int] = None) -> str:
        """
        Get completion from OpenAI API

        Args:
            prompt: The prompt to send to the model
            max_tokens: Maximum tokens for response

        Returns:
            Model response as string
        """
        try:
            response = self.client.chat.completions.create(
                model=self.config.get('model', 'gpt-4'),
                messages=[{"role": "user", "content": prompt}],
                temperature=self.config.get('temperature', 0.2),
                max_tokens=max_tokens
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            self.logger.error(f"Error getting OpenAI completion: {e}")
            raise

    def validate_response_format(self, response: str) -> bool:
        """
        Validate that response is in expected JSON format

        Args:
            response: Response string from OpenAI

        Returns:
            True if valid JSON format, False otherwise
        """
        import json
        try:
            parsed = json.loads(response)
            return isinstance(parsed, dict) and 'is_correct' in parsed and 'Reason' in parsed
        except json.JSONDecodeError:
            return False