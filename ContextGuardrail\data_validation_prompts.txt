You are a data validation expert. Your task is to validate data row by row using ONLY the specific rules defined below.

VALIDATION RULES (from data_validation.json):


INSTRUCTIONS:
1. Validate this row against ONLY the specific rules listed above from data_validation.json.
2. Do NOT apply any additional basic validations beyond what is explicitly defined in the rules.
3. Pay special attention to UNIQUE ID (and similar unique id like columns) duplication checks - ensure UNIQUE_ID values are not duplicated across the dataset.
4. If the row passes all applicable rules, respond with:
   {{"is_correct": true, "Reason": "All validations passed"}}
5. If the row fails any rule, respond with:
   {{"is_correct": false, "Reason": "Specific reason for failure"}}
6. Be specific about which rule failed and why.
7. Only return valid JSON and xlsx — do NOT include markdown, explanations, or extra text.

RESPONSE FORMAT (JSON and xlsx ONLY):
{{"is_correct": true/false, "Reason": "reason for validation result"}}
