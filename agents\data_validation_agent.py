"""
Data Validation Agent for CBRE
Validates Excel files using external configuration files and outputs results in JSON and Excel formats
"""

import os
import sys
import json
import logging
import pandas as pd
import re
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.openai_client import OpenAIClient
from shared.file_utils import FileUtils
from shared.agent_output_schema import ValidationResult, DataValidationOutput
from logger.data_validation_agent_logs import setup_validation_logger


class DataValidationAgent:
    """Data validation agent that validates Excel files using external configuration"""

    def __init__(self,
                 model_config_path: str = "config/model_config.yaml",
                 validation_rules_path: str = "data/outputs/data_validation.json",
                 validation_prompts_path: str = "ContextGuardrail/data_validation_prompts.txt"):
        """
        Initialize the data validation agent

        Args:
            model_config_path: Path to model configuration YAML
            validation_rules_path: Path to validation rules JSON
            validation_prompts_path: Path to validation prompts text file
        """
        self.model_config_path = model_config_path
        self.validation_rules_path = validation_rules_path
        self.validation_prompts_path = validation_prompts_path

        # Initialize utilities
        self.file_utils = FileUtils()
        self.openai_client = None
        self.validation_rules = []
        self.validation_prompt_template = ""

        # Setup logging
        self.logger = self._setup_logging()

        # Load configurations
        self._load_configurations()

    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        # Create logs directory if it doesn't exist
        log_dir = "data/audit_logs"
        os.makedirs(log_dir, exist_ok=True)

        # Generate log filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(log_dir, f"data_validation_{timestamp}.log")

        # Use the existing logger setup
        logger = setup_validation_logger(log_file)

        return logger

    def _load_configurations(self) -> None:
        """Load all configuration files with comprehensive validation"""
        try:
            # Validate configuration file paths exist
            self._validate_config_files()

            # Initialize OpenAI client
            self.openai_client = OpenAIClient(self.model_config_path)
            self.logger.info("OpenAI client initialized successfully")

            # Load validation rules
            self.validation_rules = self.file_utils.read_json_file(self.validation_rules_path)
            self._validate_rules_format()
            self.logger.info(f"Loaded {len(self.validation_rules)} validation rules")

            # Load validation prompt template
            self.validation_prompt_template = self.file_utils.read_text_file(self.validation_prompts_path)
            self._validate_prompt_template()
            self.logger.info("Validation prompt template loaded successfully")

        except Exception as e:
            self.logger.error(f"Error loading configurations: {e}")
            raise

    def _validate_config_files(self) -> None:
        """Validate that all required configuration files exist"""
        required_files = [
            (self.model_config_path, "Model configuration file"),
            (self.validation_rules_path, "Validation rules file"),
            (self.validation_prompts_path, "Validation prompts file")
        ]

        for file_path, description in required_files:
            if not self.file_utils.validate_file_exists(file_path):
                raise FileNotFoundError(f"{description} not found: {file_path}")

    def _validate_rules_format(self) -> None:
        """Validate that validation rules have required format"""
        if not isinstance(self.validation_rules, list):
            raise ValueError("Validation rules must be a list")

        for i, rule in enumerate(self.validation_rules):
            if not isinstance(rule, dict):
                raise ValueError(f"Rule {i} must be a dictionary")

            required_fields = ['rule', 'type']
            for field in required_fields:
                if field not in rule:
                    raise ValueError(f"Rule {i} missing required field: {field}")

            if not isinstance(rule.get('columns', []), list):
                raise ValueError(f"Rule {i} 'columns' field must be a list")

    def _validate_prompt_template(self) -> None:
        """Validate that prompt template has required placeholders"""
        if not self.validation_prompt_template:
            raise ValueError("Validation prompt template is empty")

        required_placeholders = [
            "VALIDATION RULES (from data_validation.json):",
            "is_correct",
            "Reason"
        ]

        for placeholder in required_placeholders:
            if placeholder not in self.validation_prompt_template:
                self.logger.warning(f"Prompt template missing expected content: {placeholder}")

    def validate_excel_file(self, excel_file_path: str) -> DataValidationOutput:
        """
        Validate Excel file using loaded rules and configurations

        Args:
            excel_file_path: Path to Excel file to validate

        Returns:
            DataValidationOutput containing validation results
        """
        try:
            self.logger.info(f"Starting validation of Excel file: {excel_file_path}")

            # Read Excel file
            df = self.file_utils.read_excel_file(excel_file_path)

            # Check if file is empty
            if df.empty:
                self.logger.warning("Excel file is empty")
                return self._create_empty_file_result(excel_file_path)

            # Perform validation
            validation_results = self._validate_dataframe(df)

            # Create output
            summary = DataValidationOutput.create_summary(validation_results)
            output = DataValidationOutput(
                summary=summary,
                results=validation_results,
                validation_rules=self.validation_rules,
                input_file=excel_file_path
            )

            self.logger.info(f"Validation completed. {summary.valid_rows}/{summary.total_rows} rows valid")
            return output

        except Exception as e:
            self.logger.error(f"Error validating Excel file: {e}")
            raise

    def _validate_dataframe(self, df: pd.DataFrame) -> List[ValidationResult]:
        """
        Validate DataFrame using loaded rules

        Args:
            df: DataFrame to validate

        Returns:
            List of ValidationResult objects
        """
        validation_results = []

        # Check for duplicate UNIQUE_ID values across entire dataset
        unique_id_duplicates = self._find_unique_id_duplicates(df)

        # Validate each row
        for index, row in df.iterrows():
            try:
                # Convert row to dictionary
                row_data = row.to_dict()

                # Validate row against rules
                is_valid, reason = self._validate_row(row_data, index, unique_id_duplicates)

                # Create validation result
                result = ValidationResult(
                    row_index=int(index),
                    is_correct=is_valid,
                    reason=reason,
                    row_data=row_data
                )

                validation_results.append(result)

            except Exception as e:
                self.logger.error(f"Error validating row {index}: {e}")
                # Create error result
                result = ValidationResult(
                    row_index=int(index),
                    is_correct=False,
                    reason=f"Validation error: {str(e)}",
                    row_data=row.to_dict() if hasattr(row, 'to_dict') else {}
                )
                validation_results.append(result)

        return validation_results

    def _find_unique_id_duplicates(self, df: pd.DataFrame) -> Set[Any]:
        """
        Find duplicate UNIQUE_ID values in the dataset

        Args:
            df: DataFrame to check

        Returns:
            Set of duplicate UNIQUE_ID values
        """
        duplicates = set()

        if 'UNIQUE_ID' in df.columns:
            # Find duplicates
            duplicate_mask = df.duplicated(subset=['UNIQUE_ID'], keep=False)
            if duplicate_mask.any():
                duplicates = set(df.loc[duplicate_mask, 'UNIQUE_ID'].values)
                self.logger.warning(f"Found {len(duplicates)} duplicate UNIQUE_ID values")

        return duplicates

    def _validate_row(self, row_data: Dict[str, Any], row_index: int, unique_id_duplicates: Set[Any]) -> tuple[bool, str]:
        """
        Validate a single row against all rules

        Args:
            row_data: Row data as dictionary
            row_index: Row index
            unique_id_duplicates: Set of duplicate UNIQUE_ID values

        Returns:
            Tuple of (is_valid, reason)
        """
        try:
            # Prepare prompt with rules
            rules_text = self._format_rules_for_prompt()
            prompt = self.validation_prompt_template.replace(
                "VALIDATION RULES (from data_validation.json):",
                f"VALIDATION RULES (from data_validation.json):\n{rules_text}"
            )

            # Add row data and duplicate information to prompt
            row_json = json.dumps(row_data, default=str)
            duplicate_info = ""
            if 'UNIQUE_ID' in row_data and row_data['UNIQUE_ID'] in unique_id_duplicates:
                duplicate_info = f"\nNOTE: UNIQUE_ID '{row_data['UNIQUE_ID']}' is duplicated in the dataset."

            full_prompt = f"{prompt}\n\nROW DATA TO VALIDATE:\n{row_json}{duplicate_info}"

            # Get validation from LLM with retry logic
            response = self._get_validation_with_retry(full_prompt, max_retries=3)

            # Parse response
            return self._parse_validation_response(response)

        except Exception as e:
            self.logger.error(f"Error validating row {row_index}: {e}")
            return False, f"Validation error: {str(e)}"

    def _get_validation_with_retry(self, prompt: str, max_retries: int = 3) -> str:
        """
        Get validation from LLM with retry logic

        Args:
            prompt: Prompt to send to LLM
            max_retries: Maximum number of retry attempts

        Returns:
            Response from LLM
        """
        import time

        for attempt in range(max_retries):
            try:
                response = self.openai_client.get_completion(prompt)

                # Validate response format
                if self.openai_client.validate_response_format(response):
                    return response
                else:
                    self.logger.warning(f"Invalid response format on attempt {attempt + 1}: {response}")
                    if attempt == max_retries - 1:
                        raise ValueError("Failed to get valid response format after all retries")

            except Exception as e:
                self.logger.warning(f"API call failed on attempt {attempt + 1}: {e}")
                if attempt == max_retries - 1:
                    raise

                # Wait before retry (exponential backoff)
                wait_time = 2 ** attempt
                self.logger.info(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)

        raise Exception("Failed to get validation after all retries")

    def _format_rules_for_prompt(self) -> str:
        """
        Format validation rules for inclusion in prompt

        Returns:
            Formatted rules string
        """
        rules_text = ""
        for i, rule in enumerate(self.validation_rules, 1):
            rules_text += f"{i}. {rule['rule']}\n"
            if rule.get('columns'):
                rules_text += f"   Applies to columns: {', '.join(rule['columns'])}\n"
            rules_text += f"   Type: {rule.get('type', 'unknown')}\n\n"

        return rules_text

    def _parse_validation_response(self, response: str) -> tuple[bool, str]:
        """
        Parse LLM validation response

        Args:
            response: Response from LLM

        Returns:
            Tuple of (is_valid, reason)
        """
        try:
            # Clean response - remove any markdown formatting
            cleaned_response = response.strip()
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith('```'):
                cleaned_response = cleaned_response[:-3]
            cleaned_response = cleaned_response.strip()

            # Parse JSON
            parsed = json.loads(cleaned_response)

            if isinstance(parsed, dict) and 'is_correct' in parsed and 'Reason' in parsed:
                return parsed['is_correct'], parsed['Reason']
            else:
                self.logger.warning(f"Invalid response format: {response}")
                return False, "Invalid response format from validation service"

        except json.JSONDecodeError as e:
            self.logger.error(f"Error parsing JSON response: {e}, Response: {response}")
            return False, f"Error parsing validation response: {str(e)}"
        except Exception as e:
            self.logger.error(f"Unexpected error parsing response: {e}")
            return False, f"Unexpected validation error: {str(e)}"

    def _create_empty_file_result(self, excel_file_path: str) -> DataValidationOutput:
        """
        Create result for empty file

        Args:
            excel_file_path: Path to the empty Excel file

        Returns:
            DataValidationOutput for empty file
        """
        # Check if there's a rule about empty files
        empty_file_rule = next((rule for rule in self.validation_rules
                               if "empty" in rule['rule'].lower()), None)

        if empty_file_rule:
            # File should not be empty according to rules
            result = ValidationResult(
                row_index=0,
                is_correct=False,
                reason="File is empty - violates validation rule",
                row_data={}
            )
            results = [result]
        else:
            # No specific rule about empty files
            result = ValidationResult(
                row_index=0,
                is_correct=True,
                reason="File is empty but no rule prohibits this",
                row_data={}
            )
            results = [result]

        summary = DataValidationOutput.create_summary(results)
        return DataValidationOutput(
            summary=summary,
            results=results,
            validation_rules=self.validation_rules,
            input_file=excel_file_path
        )

    def save_validation_results(self, validation_output: DataValidationOutput,
                               output_dir: str = "data/outputs") -> Dict[str, str]:
        """
        Save validation results in both JSON and Excel formats

        Args:
            validation_output: Validation results to save
            output_dir: Directory to save outputs

        Returns:
            Dictionary with paths to saved files
        """
        try:
            # Create timestamp for unique filenames
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Generate filenames
            json_filename = f"validation_results_{timestamp}.json"
            excel_filename = f"validation_results_{timestamp}.xlsx"

            json_path = os.path.join(output_dir, json_filename)
            excel_path = os.path.join(output_dir, excel_filename)

            # Save JSON output
            self.file_utils.save_json_file(validation_output.to_dict(), json_path)

            # Save Excel output
            excel_df = self._create_excel_output(validation_output)
            self.file_utils.save_excel_file(excel_df, excel_path)

            self.logger.info(f"Validation results saved to {json_path} and {excel_path}")

            return {
                'json_path': json_path,
                'excel_path': excel_path
            }

        except Exception as e:
            self.logger.error(f"Error saving validation results: {e}")
            raise

    def _create_excel_output(self, validation_output: DataValidationOutput) -> pd.DataFrame:
        """
        Create Excel-formatted DataFrame from validation results

        Args:
            validation_output: Validation results

        Returns:
            DataFrame formatted for Excel output
        """
        # Create list of dictionaries for DataFrame
        excel_data = []

        for result in validation_output.results:
            row_dict = {
                'Row_Index': result.row_index,
                'Is_Valid': result.is_correct,
                'Validation_Reason': result.reason
            }

            # Add original row data
            for key, value in result.row_data.items():
                row_dict[f'Original_{key}'] = value

            excel_data.append(row_dict)

        # Create DataFrame
        df = pd.DataFrame(excel_data)

        # Add summary sheet information as first rows
        summary_data = {
            'Row_Index': 'SUMMARY',
            'Is_Valid': '',
            'Validation_Reason': f"Total: {validation_output.summary.total_rows}, "
                               f"Valid: {validation_output.summary.valid_rows}, "
                               f"Invalid: {validation_output.summary.invalid_rows}, "
                               f"Rate: {validation_output.summary.validation_rate}%"
        }

        # Add empty columns for original data
        for col in df.columns:
            if col.startswith('Original_') and col not in summary_data:
                summary_data[col] = ''

        # Insert summary row at the beginning
        summary_df = pd.DataFrame([summary_data])
        df = pd.concat([summary_df, df], ignore_index=True)

        return df

    def run_validation(self, excel_file_path: str, output_dir: str = "data/outputs") -> Dict[str, Any]:
        """
        Run complete validation process with comprehensive error handling

        Args:
            excel_file_path: Path to Excel file to validate
            output_dir: Directory to save outputs

        Returns:
            Dictionary with validation results and output file paths
        """
        start_time = datetime.now()

        try:
            self.logger.info(f"Starting validation process for: {excel_file_path}")

            # Validate inputs
            self._validate_run_inputs(excel_file_path, output_dir)

            # Run validation
            validation_output = self.validate_excel_file(excel_file_path)

            # Save results
            output_paths = self.save_validation_results(validation_output, output_dir)

            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()

            # Prepare return data
            result = {
                'validation_summary': validation_output.summary.to_dict(),
                'total_rows_processed': validation_output.summary.total_rows,
                'validation_rate': validation_output.summary.validation_rate,
                'output_files': output_paths,
                'input_file': excel_file_path,
                'timestamp': validation_output.summary.timestamp,
                'processing_time_seconds': round(processing_time, 2)
            }

            self.logger.info(f"Validation process completed successfully. "
                           f"Rate: {validation_output.summary.validation_rate}% "
                           f"Time: {processing_time:.2f}s")

            return result

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"Error in validation process after {processing_time:.2f}s: {e}")
            raise

    def _validate_run_inputs(self, excel_file_path: str, output_dir: str) -> None:
        """
        Validate inputs for run_validation method

        Args:
            excel_file_path: Path to Excel file
            output_dir: Output directory path
        """
        # Validate input file
        if not excel_file_path or not isinstance(excel_file_path, str):
            raise ValueError("Excel file path must be a non-empty string")

        if not self.file_utils.validate_file_exists(excel_file_path):
            raise FileNotFoundError(f"Input Excel file not found: {excel_file_path}")

        # Check file extension
        file_ext = self.file_utils.get_file_extension(excel_file_path)
        if file_ext not in ['.xlsx', '.xls']:
            raise ValueError(f"Unsupported file format: {file_ext}. Only .xlsx and .xls are supported.")

        # Validate output directory
        if not output_dir or not isinstance(output_dir, str):
            raise ValueError("Output directory must be a non-empty string")

        # Create output directory if it doesn't exist
        try:
            os.makedirs(output_dir, exist_ok=True)
        except Exception as e:
            raise ValueError(f"Cannot create output directory {output_dir}: {e}")

        # Check if output directory is writable
        if not os.access(output_dir, os.W_OK):
            raise PermissionError(f"Output directory is not writable: {output_dir}")

        self.logger.debug(f"Input validation passed for file: {excel_file_path}, output: {output_dir}")


def main():
    """Main function for command line usage"""
    import argparse

    parser = argparse.ArgumentParser(description='CBRE Data Validation Agent')
    parser.add_argument('excel_file', help='Path to Excel file to validate')
    parser.add_argument('--output-dir', default='data/outputs',
                       help='Output directory for results (default: data/outputs)')
    parser.add_argument('--model-config', default='config/model_config.yaml',
                       help='Path to model configuration file')
    parser.add_argument('--validation-rules', default='data/outputs/data_validation.json',
                       help='Path to validation rules file')
    parser.add_argument('--validation-prompts', default='ContextGuardrail/data_validation_prompts.txt',
                       help='Path to validation prompts file')

    args = parser.parse_args()

    try:
        # Initialize agent
        agent = DataValidationAgent(
            model_config_path=args.model_config,
            validation_rules_path=args.validation_rules,
            validation_prompts_path=args.validation_prompts
        )

        # Run validation
        result = agent.run_validation(args.excel_file, args.output_dir)

        # Print results
        print("\n" + "="*50)
        print("VALIDATION COMPLETED")
        print("="*50)
        print(f"Input File: {result['input_file']}")
        print(f"Total Rows: {result['total_rows_processed']}")
        print(f"Validation Rate: {result['validation_rate']}%")
        print(f"JSON Output: {result['output_files']['json_path']}")
        print(f"Excel Output: {result['output_files']['excel_path']}")
        print(f"Timestamp: {result['timestamp']}")
        print("="*50)

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()