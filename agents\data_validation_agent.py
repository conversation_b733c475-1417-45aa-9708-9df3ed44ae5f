"""
Data Validation Agent for CBRE
Validates Excel files using external configuration files and outputs results in JSON and Excel formats
"""

import os
import json
import yaml
import pandas as pd
from openai import OpenAI
from datetime import datetime
from typing import Dict, List, Any


class DataValidationAgent:
    """Minimal data validation agent that uses external configuration files only"""

    def __init__(self):
        """Initialize the data validation agent"""
        self.openai_client = None
        self.validation_rules = []
        self.validation_prompt_template = ""
        self.model = ""
        self.temperature = 0.2

        # Load configurations
        self._load_configurations()

    def _load_configurations(self) -> None:
        """Load all external configuration files"""
        # Load model configuration
        with open("config/model_config.yaml", 'r') as file:
            config = yaml.safe_load(file)
            openai_config = config['openai']

        # Initialize OpenAI client
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable not set")

        self.openai_client = OpenAI(api_key=api_key)
        self.model = openai_config['model']
        self.temperature = openai_config['temperature']

        # Load validation rules from JSON
        with open("data/outputs/data_validation.json", 'r') as file:
            self.validation_rules = json.load(file)

        # Load validation prompt template
        with open("ContextGuardrail/data_validation_prompts.txt", 'r') as file:
            self.validation_prompt_template = file.read()

    def validate_excel_file(self, excel_file_path: str) -> Dict[str, Any]:
        """
        Validate Excel file using external rules and prompt template

        Args:
            excel_file_path: Path to Excel file to validate

        Returns:
            Dictionary with validation results
        """
        # Read Excel file
        df = pd.read_excel(excel_file_path)

        # Check if file is empty
        if df.empty:
            return {
                'summary': {'total_rows': 0, 'valid_rows': 0, 'invalid_rows': 0, 'validation_rate': 0},
                'results': [],
                'timestamp': datetime.now().isoformat()
            }

        # Find duplicate UNIQUE_ID values for duplicate check rule
        unique_id_duplicates = set()
        if 'UNIQUE_ID' in df.columns:
            duplicate_mask = df.duplicated(subset=['UNIQUE_ID'], keep=False)
            if duplicate_mask.any():
                unique_id_duplicates = set(df.loc[duplicate_mask, 'UNIQUE_ID'].values)

        # Validate each row
        validation_results = []
        for index, row in df.iterrows():
            row_data = row.to_dict()

            # Create prompt with rules and row data
            rules_text = self._format_rules_for_prompt()
            prompt = self.validation_prompt_template.replace(
                "VALIDATION RULES (from data_validation.json):",
                f"VALIDATION RULES (from data_validation.json):\n{rules_text}"
            )

            # Add duplicate information if applicable
            duplicate_info = ""
            if 'UNIQUE_ID' in row_data and row_data['UNIQUE_ID'] in unique_id_duplicates:
                duplicate_info = f"\nNOTE: UNIQUE_ID '{row_data['UNIQUE_ID']}' is duplicated in the dataset."

            row_json = json.dumps(row_data, default=str)
            full_prompt = f"{prompt}\n\nROW DATA TO VALIDATE:\n{row_json}{duplicate_info}"

            # Get validation from LLM
            try:
                response = self.openai_client.chat.completions.create(
                    model=self.model,
                    messages=[{"role": "user", "content": full_prompt}],
                    temperature=self.temperature
                )

                llm_response = response.choices[0].message.content.strip()

                # Parse LLM response
                is_valid, reason = self._parse_llm_response(llm_response)

                validation_results.append({
                    'row_index': int(index),
                    'is_correct': is_valid,
                    'reason': reason,
                    'row_data': row_data
                })

            except Exception as e:
                validation_results.append({
                    'row_index': int(index),
                    'is_correct': False,
                    'reason': f"Validation error: {str(e)}",
                    'row_data': row_data
                })

        # Create summary
        total_rows = len(validation_results)
        valid_rows = sum(1 for result in validation_results if result['is_correct'])
        invalid_rows = total_rows - valid_rows
        validation_rate = (valid_rows / total_rows * 100) if total_rows > 0 else 0

        return {
            'summary': {
                'total_rows': total_rows,
                'valid_rows': valid_rows,
                'invalid_rows': invalid_rows,
                'validation_rate': round(validation_rate, 2)
            },
            'results': validation_results,
            'validation_rules': self.validation_rules,
            'input_file': excel_file_path,
            'timestamp': datetime.now().isoformat()
        }

    def _format_rules_for_prompt(self) -> str:
        """Format validation rules for inclusion in prompt"""
        rules_text = ""
        for i, rule in enumerate(self.validation_rules, 1):
            rules_text += f"{i}. {rule['rule']}\n"
            if rule.get('columns'):
                rules_text += f"   Applies to columns: {', '.join(rule['columns'])}\n"
            rules_text += f"   Type: {rule.get('type', 'unknown')}\n\n"
        return rules_text

    def _parse_llm_response(self, response: str) -> tuple[bool, str]:
        """Parse LLM validation response"""
        try:
            # Clean response - remove any markdown formatting
            cleaned_response = response.strip()
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith('```'):
                cleaned_response = cleaned_response[:-3]
            cleaned_response = cleaned_response.strip()

            # Parse JSON
            parsed = json.loads(cleaned_response)

            if isinstance(parsed, dict) and 'is_correct' in parsed and 'Reason' in parsed:
                return parsed['is_correct'], parsed['Reason']
            else:
                return False, "Invalid response format from validation service"

        except json.JSONDecodeError:
            return False, f"Error parsing validation response: {response}"
        except Exception as e:
            return False, f"Unexpected validation error: {str(e)}"

    def save_results(self, validation_output: Dict[str, Any], output_dir: str = "data/outputs") -> Dict[str, str]:
        """
        Save validation results in both JSON and Excel formats

        Args:
            validation_output: Validation results to save
            output_dir: Directory to save outputs

        Returns:
            Dictionary with paths to saved files
        """
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Create timestamp for unique filenames
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Generate filenames
        json_filename = f"validation_results_{timestamp}.json"
        excel_filename = f"validation_results_{timestamp}.xlsx"

        json_path = os.path.join(output_dir, json_filename)
        excel_path = os.path.join(output_dir, excel_filename)

        # Save JSON output
        with open(json_path, 'w') as file:
            json.dump(validation_output, file, indent=2, default=str)

        # Save Excel output
        excel_data = []
        for result in validation_output['results']:
            row_dict = {
                'Row_Index': result['row_index'],
                'Is_Valid': result['is_correct'],
                'Validation_Reason': result['reason']
            }
            # Add original row data
            for key, value in result['row_data'].items():
                row_dict[f'Original_{key}'] = value
            excel_data.append(row_dict)

        # Create DataFrame and save
        df = pd.DataFrame(excel_data)
        df.to_excel(excel_path, index=False)

        return {
            'json_path': json_path,
            'excel_path': excel_path
        }

    def run_validation(self, excel_file_path: str, output_dir: str = "data/outputs") -> Dict[str, Any]:
        """
        Run complete validation process

        Args:
            excel_file_path: Path to Excel file to validate
            output_dir: Directory to save outputs

        Returns:
            Dictionary with validation results and output file paths
        """
        # Validate Excel file
        validation_output = self.validate_excel_file(excel_file_path)

        # Save results
        output_paths = self.save_results(validation_output, output_dir)

        # Add output paths to result
        validation_output['output_files'] = output_paths

        return validation_output


def main():
    """Main function for command line usage"""
    import argparse

    parser = argparse.ArgumentParser(description='CBRE Data Validation Agent')
    parser.add_argument('excel_file', help='Path to Excel file to validate')
    parser.add_argument('--output-dir', default='data/outputs',
                       help='Output directory for results (default: data/outputs)')

    args = parser.parse_args()

    try:
        # Initialize agent
        agent = DataValidationAgent()

        # Run validation
        result = agent.run_validation(args.excel_file, args.output_dir)

        # Print results
        print("\n" + "="*50)
        print("VALIDATION COMPLETED")
        print("="*50)
        print(f"Input File: {result['input_file']}")
        print(f"Total Rows: {result['summary']['total_rows']}")
        print(f"Valid Rows: {result['summary']['valid_rows']}")
        print(f"Invalid Rows: {result['summary']['invalid_rows']}")
        print(f"Validation Rate: {result['summary']['validation_rate']}%")
        print(f"JSON Output: {result['output_files']['json_path']}")
        print(f"Excel Output: {result['output_files']['excel_path']}")
        print(f"Timestamp: {result['timestamp']}")
        print("="*50)

    except Exception as e:
        print(f"Error: {e}")
        import sys
        sys.exit(1)


if __name__ == "__main__":
    main()